import db from "@/db";
import { jobVacancy, user } from "@/db/schema";
import { auth } from "@/lib/auth";
import type { patchUserSchema, userFilterSchema } from "@/lib/schemas";
import { and, count, eq, inArray, like } from "drizzle-orm";

export class UsersOperations {
  static async findMany(
    params: Partial<typeof userFilterSchema._type> & {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortDirection?: string;
      hasVacancies?: boolean;
      fields?: string[];
    },
  ) {
    const {
      page = 1,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
      hasVacancies,
      fields,
      ...filters
    } = params || {};
    const offset = limit ? (page - 1) * limit : undefined;

    const defaultColumns = {
      id: true,
      name: true,
      description: true,
      email: true,
      image: true,
      phoneNumber: true,
      dateOfBirth: true,
      gender: true,
      role: true,
      createdAt: true,
      updatedAt: true,
    };

    const selectColumns =
      fields && fields.length > 0
        ? fields.reduce(
            (acc, field) => {
              if (field in defaultColumns) {
                acc[field] = true;
              }
              return acc;
            },
            {} as Record<string, boolean>,
          )
        : defaultColumns;

    let userIdsWithVacancies: string[] | null = null;
    if (hasVacancies) {
      const usersWithVacancies = await db
        .selectDistinct({ authorId: jobVacancy.authorId })
        .from(jobVacancy)
        .where(eq(jobVacancy.status, "published"));

      userIdsWithVacancies = usersWithVacancies.map((u) => u.authorId);

      if (userIdsWithVacancies.length === 0) {
        // No users have published vacancies
        return {
          items: [],
          total: 0,
          page,
          limit,
          pages: 0,
        };
      }
    }

    const [usersList, [{ total }]] = await Promise.all([
      db.query.user.findMany({
        columns: selectColumns,
        where: {
          ...(filters.name && { name: { like: `%${filters.name}%` } }),
          ...(filters.description && {
            description: { like: `%${filters.description}%` },
          }),
          ...(filters.email && { email: { like: `%${filters.email}%` } }),
          ...(filters.role &&
          Array.isArray(filters.role) &&
          filters.role.length === 1
            ? { role: filters.role[0] as "admin" | "author" | "editor" }
            : filters.role &&
                Array.isArray(filters.role) &&
                filters.role.length > 1
              ? {
                  role: {
                    in: filters.role as ("admin" | "author" | "editor")[],
                  },
                }
              : {}),
          ...(filters.gender && { gender: filters.gender }),
          ...(typeof filters.banned === "boolean" && {
            banned: filters.banned,
          }),
          ...(filters.createdAt && !Number.isNaN(Date.parse(filters.createdAt))
            ? { createdAt: { eq: new Date(filters.createdAt) } }
            : {}),
          ...(filters.updatedAt && !Number.isNaN(Date.parse(filters.updatedAt))
            ? { updatedAt: { eq: new Date(filters.updatedAt) } }
            : {}),
          ...(userIdsWithVacancies ? { id: { in: userIdsWithVacancies } } : {}),
        },
        limit,
        offset,
        orderBy: { [sortBy]: sortDirection },
      }),
      db
        .select({ total: count() })
        .from(user)
        .where(
          and(
            filters.name ? like(user.name, `%${filters.name}%`) : undefined,
            filters.description
              ? like(user.description, `%${filters.description}%`)
              : undefined,
            filters.email ? like(user.email, `%${filters.email}%`) : undefined,
            filters.role &&
              Array.isArray(filters.role) &&
              filters.role.length === 1
              ? eq(user.role, filters.role[0] as "admin" | "author" | "editor")
              : filters.role &&
                  Array.isArray(filters.role) &&
                  filters.role.length > 1
                ? inArray(
                    user.role,
                    filters.role as ("admin" | "author" | "editor")[],
                  )
                : undefined,
            filters.gender ? eq(user.gender, filters.gender) : undefined,
            typeof filters.banned === "boolean"
              ? eq(user.banned, filters.banned)
              : undefined,
            filters.createdAt
              ? eq(user.createdAt, new Date(filters.createdAt))
              : undefined,
            filters.updatedAt
              ? eq(user.updatedAt, new Date(filters.updatedAt))
              : undefined,
            userIdsWithVacancies
              ? inArray(user.id, userIdsWithVacancies)
              : undefined,
          ),
        ),
    ]);

    return {
      items: usersList,
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
    };
  }

  static async findById(id: string) {
    return db.query.user.findFirst({
      where: { id },
      columns: {
        id: true,
        name: true,
        description: true,
        email: true,
        image: true,
        phoneNumber: true,
        dateOfBirth: true,
        gender: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  static async patch(
    id: string,
    updates: Partial<typeof patchUserSchema._type>,
  ) {
    const [updated] = await db
      .update(user)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(user.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db.delete(user).where(eq(user.id, id)).returning();
    return deleted;
  }

  static async deleteAccount(
    userId: string,
    password: string,
    deletionReason: string,
  ) {
    // First verify the password by attempting to sign in
    const userToDelete = await db.query.user.findFirst({
      where: { id: userId },
      columns: { email: true },
    });

    if (!userToDelete) {
      throw new Error("User not found");
    }

    // Verify password using better-auth sign-in
    try {
      const signInResult = await auth.api.signInEmail({
        body: {
          email: userToDelete.email,
          password: password,
        },
      });

      if (!signInResult.user || signInResult.user.id !== userId) {
        throw new Error("Invalid password");
      }
    } catch {
      throw new Error("Invalid password");
    }

    // Perform account deletion in a transaction
    return await db.transaction(async (tx) => {
      // Archive all published vacancies
      await tx
        .update(jobVacancy)
        .set({
          status: "archived",
          archivedAt: new Date(),
        })
        .where(
          and(
            eq(jobVacancy.authorId, userId),
            eq(jobVacancy.status, "published"),
          ),
        );

      // Delete all draft vacancies
      await tx
        .delete(jobVacancy)
        .where(
          and(eq(jobVacancy.authorId, userId), eq(jobVacancy.status, "draft")),
        );

      // Anonymize user data
      const [anonymizedUser] = await tx
        .update(user)
        .set({
          email: "",
          image: null,
          dateOfBirth: null,
          phoneNumber: null,
          name: "Deleted User",
          description: null,
          archivedAt: new Date(),
        })
        .where(eq(user.id, userId))
        .returning();

      return {
        success: true,
        deletionReason,
        deletedAt: new Date(),
        user: anonymizedUser,
      };
    });
  }

  static async findVacancyAuthors() {
    const authors = await db
      .selectDistinct({
        id: user.id,
        name: user.name,
        image: user.image,
      })
      .from(user)
      .innerJoin(jobVacancy, eq(user.id, jobVacancy.authorId))
      .where(eq(jobVacancy.status, "published"));

    return authors;
  }
}
