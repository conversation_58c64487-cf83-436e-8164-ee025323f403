{"name": "gw-crm-api", "type": "module", "version": "1.0.0", "scripts": {"dev": "tsx watch src/index.ts", "start": "node ./dist/src/index.js", "typecheck": "tsc --noEmit", "lint": "biome lint .", "lint:fix": "biome check --write .", "format": "biome format --write .", "format:check": "biome format .", "check": "biome check .", "test": "cross-env NODE_ENV=test vitest run", "test:watch": "cross-env NODE_ENV=test vitest", "test:coverage": "cross-env NODE_ENV=test vitest run --coverage", "build": "tsc && tsc-alias", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "docker:build:dev": "docker build -t gw-crm-api:dev .", "docker:build:prod": "docker build -t gw-crm-api:prod .", "docker:dev": "docker compose -f docker-compose.dev.yml up -d", "docker:dev:build": "docker compose -f docker-compose.dev.yml build", "docker:dev:down": "docker compose -f docker-compose.dev.yml down", "docker:dev:logs": "docker compose -f docker-compose.dev.yml logs -f", "docker:prod": "docker compose -f docker-compose.prod.yml up -d", "docker:prod:build": "docker compose -f docker-compose.prod.yml build", "docker:prod:down": "docker compose -f docker-compose.prod.yml down", "docker:prod:logs": "docker compose -f docker-compose.prod.yml logs -f", "docker:stack:dev": "docker stack deploy -c docker-compose.dev.yml gw-crm-api-dev", "docker:stack:dev:remove": "docker stack rm gw-crm-api-dev", "docker:stack:dev:services": "docker stack services gw-crm-api-dev", "docker:stack:dev:ps": "docker stack ps gw-crm-api-dev", "docker:stack:prod": "docker stack deploy -c docker-compose.prod.yml gw-crm-api-prod", "docker:stack:prod:remove": "docker stack rm gw-crm-api-prod", "docker:stack:prod:services": "docker stack services gw-crm-api-prod", "docker:stack:prod:ps": "docker stack ps gw-crm-api-prod"}, "dependencies": {"@hono/arktype-validator": "^2.0.0", "@hono/node-server": "^1.13.8", "@hono/zod-validator": "^0.4.3", "@paralleldrive/cuid2": "^2.2.2", "@scalar/hono-api-reference": "^0.5.183", "arktype": "^2.1.19", "better-auth": "^1.2.3", "dotenv": "^16.4.7", "dotenv-expand": "^12.0.1", "drizzle-arktype": "^0.1.2", "drizzle-orm": "1.0.0-beta.1-2e234ba", "drizzle-zod": "^0.7.0", "handlebars": "^4.7.8", "hono": "^4.7.4", "hono-openapi": "^0.4.6", "hono-pino": "^0.7.2", "ioredis": "^5.6.0", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.64.0", "openapi-merge": "^1.3.3", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "resend": "^4.1.2", "stoker": "1.4.2", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^22.13.10", "@types/node-cron": "^3.0.11", "@types/node-telegram-bot-api": "^0.64.0", "@vitest/coverage-v8": "3.0.8", "atlassian-openapi": "^1.0.21", "cross-env": "^7.0.3", "drizzle-kit": "1.0.0-beta.1-2e234ba", "tsc-alias": "^1.8.11", "tsx": "^4.19.3", "typescript": "^5.8.2", "vitest": "^3.0.8"}}